# -*- coding: utf-8 -*-
"""Untitled3.ipynb

Automatically generated by Colab.

Original file is located at
    https://colab.research.google.com/drive/18Ge_KrWt_1DGXeYnsx6QHlXeheSDLEJJ
"""

# ======================== 0. SETUP (Colab) ========================
!pip -q install --upgrade pip
!pip -q install "torch>=2.2" torchvision --index-url https://download.pytorch.org/whl/cu121
!pip -q install "transformers>=4.41" "diffusers>=0.27" accelerate
!pip -q install scikit-learn tqdm opencv-python pillow "lpips>=0.1.4"

from google.colab import drive; drive.mount('/content/drive')

import os, json, random, tarfile, urllib.request, shutil, math
from pathlib import Path
import numpy as np
from PIL import Image
import cv2
from tqdm import tqdm

import torch, torch.nn as nn, torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import torchvision.transforms as T
from torchvision.datasets import ImageFolder

from transformers import CLIPModel, CLIPProcessor
from diffusers import AutoencoderKL, StableDiffusionControlNetImg2ImgPipeline, ControlNetModel
import lpips

# Reproducibility
def set_seed(seed=0):
    random.seed(seed); np.random.seed(seed)
    torch.manual_seed(seed); torch.cuda.manual_seed_all(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False
set_seed(0)

DEVICE = "cuda" if torch.cuda.is_available() else "cpu"
DTYPE  = torch.float16 if torch.cuda.is_available() else torch.float32

# ======================== 1. PATHS & DATA =========================
DRIVE_JSON_PATH = "/content/drive/MyDrive/imagenette_ccso_enhanced_robust_20250821_135956.json"  # <-- your file
DATA_ROOT = Path("/content/imagenette_full")
OUTPUT_ROOT = Path("/content/drive/MyDrive/distilled_outputs")
OUTPUT_ROOT.mkdir(parents=True, exist_ok=True)

# Imagenette WNIDs (official 10-class subset)
WNID2NAME = {
    "n01440764": "tench",
    "n02102040": "English springer",
    "n02979186": "cassette player",
    "n03000684": "chain saw",
    "n03028079": "church",
    "n03394916": "French horn",
    "n03417042": "garbage truck",
    "n03425413": "gas pump",
    "n03445777": "golf ball",
    "n03888257": "parachute",
}
NAME2WNID = {v:k for k,v in WNID2NAME.items()}

def download_and_extract_imagenette_full(dest=DATA_ROOT):
    url = "https://s3.amazonaws.com/fast-ai-imageclas/imagenette2.tgz"  # full size
    dest.mkdir(parents=True, exist_ok=True)
    tgz_path = dest / "imagenette2.tgz"
    if not tgz_path.exists():
        print("Downloading imagenette2.tgz ...")
        urllib.request.urlretrieve(url, tgz_path)
    print("Extracting archive...")
    with tarfile.open(tgz_path, "r:gz") as tar:
        tar.extractall(path=dest)
    extracted_path = dest / "imagenette2"
    if not extracted_path.exists():
        raise FileNotFoundError(f"Expected {extracted_path}")
    return extracted_path

IMAGENETTE_PATH = download_and_extract_imagenette_full()
print("Dataset:", IMAGENETTE_PATH)

# ======================== 2. LOAD YOUR JSON =======================
with open(DRIVE_JSON_PATH, "r") as f:
    ccso_json = json.load(f)
assert "classes" in ccso_json and isinstance(ccso_json["classes"], dict)
for cname in WNID2NAME.values():
    assert cname in ccso_json["classes"], f"Missing class {cname}"
    assert "descriptions" in ccso_json["classes"][cname]

# Class -> list[str] (all your Gemini descriptions)
CLASS_TEXTS = {name: ccso_json["classes"][name]["descriptions"] for name in WNID2NAME.values()}

# ======================== 3. MODELS (CLIP / VAE / ControlNet) =====
clip_name = "openai/clip-vit-large-patch14"
clip_model = CLIPModel.from_pretrained(clip_name).eval().to(DEVICE)
clip_proc  = CLIPProcessor.from_pretrained(clip_name)

vae = AutoencoderKL.from_pretrained("runwayml/stable-diffusion-v1-5", subfolder="vae",
                                    torch_dtype=DTYPE).to(DEVICE).eval()
VAE_SCALE = 0.18215  # SD1.x latent scaling

# Structural constraint: Canny ControlNet (fast, low VRAM)
cn_canny = ControlNetModel.from_pretrained("lllyasviel/sd-controlnet-canny", torch_dtype=DTYPE)
pipe = StableDiffusionControlNetImg2ImgPipeline.from_pretrained(
    "runwayml/stable-diffusion-v1-5", controlnet=cn_canny,
    torch_dtype=DTYPE, safety_checker=None
).to(DEVICE)

# LPIPS perceptual (for VLPR regularization)
lpips_fn = lpips.LPIPS(net='vgg').to(DEVICE).eval()

# ======================== 4. FEATURE EXTRACTORS ===================
@torch.no_grad()
def clip_text_feats(texts, bs=64):
    out = []
    for i in range(0, len(texts), bs):
        batch = texts[i:i+bs]
        toks = clip_proc(text=batch, return_tensors="pt", padding=True, truncation=True).to(DEVICE)
        feats = clip_model.get_text_features(**toks)
        out.append(F.normalize(feats, dim=-1).detach().to(torch.float32).cpu())
        del toks, feats
        torch.cuda.empty_cache()
    return torch.cat(out, dim=0)

@torch.no_grad()
def clip_image_feat_from_pil(pil: Image.Image):
    ip = clip_proc(images=pil, return_tensors="pt").to(DEVICE)
    feats = clip_model.get_image_features(**ip)
    return F.normalize(feats, dim=-1).squeeze(0).detach().to(torch.float32).cpu()

@torch.no_grad()
def vae_encode_pils(pils, size=(512,512)):
    # encode PIL list -> latent tensor [B,4,64,64] (float32, CPU)
    tens = [T.ToTensor()(im.resize(size, Image.BICUBIC)) for im in pils]
    x = torch.stack(tens, 0).to(DEVICE, dtype=DTYPE)
    x = x * 2 - 1
    z = vae.encode(x).latent_dist.sample() * VAE_SCALE
    out = z.detach().float().cpu()
    del x, z; torch.cuda.empty_cache()
    return out

# ======================== 5. BUILD PER-IMAGE FEATURES =============
train_folder = ImageFolder(IMAGENETTE_PATH/"train")
train_items = train_folder.samples  # list of (path, class_idx)

# Precompute per-class text embeddings once
CLASS_TEXT_FEATS = {name: clip_text_feats(CLASS_TEXTS[name]) for name in WNID2NAME.values()}

BATCH = 16
CLIP_I, VAE_Z, BEST_T, BEST_TF, Y, PATHS = [], [], [], [], [], []
for s in tqdm(range(0, len(train_items), BATCH), desc="Featurizing train images"):
    chunk = train_items[s:s+BATCH]
    pils, yidxs, paths = [], [], []
    for p, y in chunk:
        pils.append(Image.open(p).convert("RGB"))
        yidxs.append(y); paths.append(p)
    # CLIP image features (per image)
    img_feats = [clip_image_feat_from_pil(pil) for pil in pils]
    # VAE latents
    latents = vae_encode_pils(pils)
    # pick best text in its class (max cosine with CLIP image feature)
    for i, (pil, y, pth) in enumerate(zip(pils, yidxs, paths)):
        wnid = Path(pth).parent.name
        cname = WNID2NAME[wnid]
        tf_mat = CLASS_TEXT_FEATS[cname]               # [M, D] CPU
        sims = tf_mat @ img_feats[i]                   # [M]
        idx = int(torch.argmax(sims).item())           # best sentence index
        CLIP_I.append(img_feats[i])
        VAE_Z.append(latents[i])
        BEST_T.append(CLASS_TEXTS[cname][idx])
        BEST_TF.append(tf_mat[idx])
        Y.append(y)
        PATHS.append(pth)

CLIP_I = torch.stack(CLIP_I)        # [N, D]
VAE_Z  = torch.stack(VAE_Z)         # [N, 4, 64, 64]
BEST_TF= torch.stack(BEST_TF)       # [N, D]
Y      = torch.tensor(Y)            # [N]

# ======================== 6. OUTLIER REMOVAL (LOF) ================
from sklearn.neighbors import LocalOutlierFactor
CONTAM = 0.05
keep = torch.zeros(len(Y), dtype=torch.bool)
for c in sorted(set(Y.tolist())):
    idx = (Y == c).nonzero(as_tuple=False).squeeze(1).numpy()
    feats = (F.normalize(CLIP_I[idx], dim=-1) + F.normalize(BEST_TF[idx], dim=-1))/2.0
    feats = feats.numpy()
    lof = LocalOutlierFactor(n_neighbors=10, contamination=CONTAM)
    inlier = (lof.fit_predict(feats) == 1)
    keep[idx[inlier]] = True

CLIP_I, VAE_Z, BEST_TF, Y = CLIP_I[keep], VAE_Z[keep], BEST_TF[keep], Y[keep]
PATHS = [p for p,k in zip(PATHS, keep.tolist()) if k]
BEST_T = [t for t,k in zip(BEST_T, keep.tolist()) if k]

# ======================== 7. MODERN SPHERICAL K-MEANS =============
# (pure NumPy implementation; cosine geometry on unit sphere)
from sklearn.metrics.pairwise import cosine_similarity

class ModernSphericalKMeans:
    def __init__(self, n_clusters, max_iter=200, n_init=10, random_state=0, tol=1e-4):
        self.n_clusters, self.max_iter, self.n_init = n_clusters, max_iter, n_init
        self.random_state, self.tol = random_state, tol
        self.labels_ = None; self.cluster_centers_ = None; self.inertia_ = None

    def _norm(self, X):
        n = np.linalg.norm(X, axis=1, keepdims=True); n = np.where(n==0, 1, n); return X/n

    def _init_centers(self, X):
        rng = np.random.RandomState(self.random_state)
        n, d = X.shape
        centers = np.empty((self.n_clusters, d), dtype=X.dtype)
        centers[0] = X[rng.randint(n)]
        for c in range(1, self.n_clusters):
            sims = cosine_similarity(X, centers[:c])
            dists = 1 - np.max(sims, axis=1)
            prob = (dists**2); prob /= prob.sum()
            centers[c] = X[np.searchsorted(np.cumsum(prob), rng.rand())]
        return self._norm(centers)

    def _assign(self, X, centers):
        return np.argmax(cosine_similarity(X, centers), axis=1)

    def _update(self, X, labels):
        K = self.n_clusters; d = X.shape[1]
        centers = np.empty((K, d), dtype=X.dtype)
        for k in range(K):
            mask = (labels==k)
            if np.sum(mask)>0:
                m = X[mask].mean(0); centers[k] = m/np.linalg.norm(m)
            else:
                centers[k] = self.cluster_centers_[k] if self.cluster_centers_ is not None else X[k%len(X)]
        return centers

    def _inertia(self, X, labels, centers):
        J = 0.0
        for k in range(self.n_clusters):
            m = (labels==k)
            if np.sum(m)>0:
                sims = cosine_similarity(X[m], centers[k:k+1]).flatten()
                d = 1 - sims
                J += (d**2).sum()
        return J

    def fit_predict(self, X):
        X = self._norm(X)
        bestJ = np.inf
        bestL, bestC = None, None
        for _ in range(self.n_init):
            C = self._init_centers(X)
            for _ in range(self.max_iter):
                L = self._assign(X, C)
                C_new = self._update(X, L)
                shift = np.mean([1 - cosine_similarity(C[i:i+1], C_new[i:i+1])[0,0] for i in range(self.n_clusters)])
                C = C_new
                if shift < self.tol: break
            J = self._inertia(X, L, C)
            if J < bestJ: bestJ, bestL, bestC = J, L.copy(), C.copy()
        self.labels_, self.cluster_centers_, self.inertia_ = bestL, bestC, bestJ
        return self.labels_

# ======================== 8. JVL-C + CCSO =========================
ALPHA = 0.6  # image/text fusion
GAMMA = 0.5  # CCSO contrastive shift

def fuse_feats(ci, ct, alpha=ALPHA):
    return F.normalize(alpha*F.normalize(ci,dim=-1) + (1-alpha)*F.normalize(ct,dim=-1), dim=-1)

class ClusterInfo:
    def __init__(self, member_idx, Zc, medoid_idx, text_pool):
        self.member_idx = member_idx            # indices into filtered arrays
        self.Zc = Zc                            # [4,64,64] float32
        self.medoid_idx = medoid_idx            # global index in filtered arrays
        self.text_pool = text_pool              # list[str] (best per member)

def canny_from_path(img_path, size=512, low=100, high=200):
    pil = Image.open(img_path).convert("RGB").resize((size,size), Image.BICUBIC)
    arr = np.array(pil)
    edges = cv2.Canny(arr, low, high)
    edges = np.stack([edges]*3, -1)
    return Image.fromarray(edges)

def jvlc_build_prototypes(IPC):
    clusters_by_class = {}
    folder_map = train_folder.class_to_idx  # wnid -> class_idx
    for wnid, cname in WNID2NAME.items():
        if wnid not in folder_map: clusters_by_class[cname]=[]; continue
        cls_idx = folder_map[wnid]
        idx = (Y == cls_idx).nonzero(as_tuple=False).squeeze(1)
        if idx.numel()==0: clusters_by_class[cname]=[]; continue
        F_fused = fuse_feats(CLIP_I[idx], BEST_TF[idx]).numpy()
        K = min(IPC, len(idx))
        skm = ModernSphericalKMeans(n_clusters=K, n_init=10, max_iter=200, random_state=0)
        labels = skm.fit_predict(F_fused)
        cls_clusters = []
        for k in range(K):
            members = idx[labels==k]
            if members.numel()==0: continue
            Zc = VAE_Z[members].mean(0)
            dif = (VAE_Z[members]-Zc.unsqueeze(0)).flatten(1)
            medoid_local = int(torch.argmin((dif**2).sum(1)).item())
            medoid_global = int(members[medoid_local])
            tpool = [BEST_T[i] for i in members.tolist()]
            cls_clusters.append(ClusterInfo(members.numpy(), Zc.cpu(), medoid_global, tpool))
        clusters_by_class[cname] = cls_clusters
    return clusters_by_class

@torch.no_grad()
def text_embed(texts):   # normalized CLIP text features
    return clip_text_feats(texts)

def ccso_select(clusters_by_class):
    """
    Returns: dict[cname] -> list[(prototype_sentence, prototype_feat)]
    """
    text_proto_by_class = {}
    for cname, clusters in clusters_by_class.items():
        if not clusters:
            text_proto_by_class[cname] = []; continue
        centroids, embeds_per_cluster = [], []
        for cl in clusters:
            E = text_embed(cl.text_pool)            # [m,D]
            embeds_per_cluster.append(E)
            centroids.append(F.normalize(E.mean(0, keepdim=True), dim=-1))
        tplist = []
        for i, cl in enumerate(clusters):
            others = [centroids[j] for j in range(len(centroids)) if j!=i]
            if others:
                mu_notC = F.normalize(torch.vstack(others).mean(0, keepdim=True), dim=-1)
                Pstar = F.normalize(centroids[i] - GAMMA*mu_notC, dim=-1)
            else:
                Pstar = centroids[i]
            sims = (embeds_per_cluster[i] @ Pstar.T).squeeze(1)
            best = int(torch.argmax(sims).item())
            tplist.append((cl.text_pool[best], Pstar.squeeze(0).cpu()))
        text_proto_by_class[cname] = tplist
    return text_proto_by_class

# ======================== 9. SYNTHESIS (ControlNet Canny) =========
GUIDANCE = 7.5
STEPS    = 30
STRENGTH = 0.8

@torch.no_grad()
def vae_decode_to_pil(Z, size=512):
    z = Z.unsqueeze(0).to(DEVICE, dtype=DTYPE) / VAE_SCALE
    im = vae.decode(z).sample
    im = (im/2 + 0.5).clamp(0,1)
    arr = (im[0].detach().float().cpu().permute(1,2,0).numpy()*255).astype(np.uint8)
    return Image.fromarray(arr).resize((size,size), Image.BICUBIC)

def synthesize_for_ipc(IPC, clusters_by_class, text_proto_by_class, out_root=OUTPUT_ROOT):
    out_dir = Path(out_root)/f"ipc{IPC}"
    if out_dir.exists(): shutil.rmtree(out_dir)
    for cname in WNID2NAME.values(): (out_dir/cname).mkdir(parents=True, exist_ok=True)

    records = []
    for cname, clusters in clusters_by_class.items():
        if not clusters: continue
        tplist = text_proto_by_class[cname]
        for k, cl in enumerate(clusters):
            Zc = cl.Zc
            text, _ = tplist[k]
            init_img = vae_decode_to_pil(Zc, size=512)
            control_img = canny_from_path(PATHS[cl.medoid_idx], size=512)
            out = pipe(prompt=text, image=init_img, control_image=control_img,
                       num_inference_steps=STEPS, guidance_scale=GUIDANCE, strength=STRENGTH,
                       height=512, width=512)
            img = out.images[0].resize((256,256), Image.BICUBIC)  # 256 base
            fn = f"{cname}_c{k:02d}.png"
            img.save(out_dir/cname/fn)
            records.append((cname, k, fn, text))
    return out_dir, records

# ======================== 10. VLPR (post-synthesis refine) ========
LR = 5e-2
REFINE_STEPS = 100
LAMB_C, LAMB_V, LAMB_P = 1.0, 0.15, 0.05   # InfoNCE, anchor to Z_init, LPIPS

@torch.no_grad()
def clip_img_feats_from_pil(pil: Image.Image):
    ip = clip_proc(images=pil, return_tensors="pt").to(DEVICE)
    f = clip_model.get_image_features(**ip)
    return F.normalize(f, dim=-1).squeeze(0)

@torch.no_grad()
def vae_encode_from_pil(pil: Image.Image):
    t = T.ToTensor()(pil).unsqueeze(0).to(DEVICE, dtype=DTYPE)
    t = T.Resize((512,512), antialias=True)(t)
    t = t*2 - 1
    z = vae.encode(t).latent_dist.sample() * VAE_SCALE
    return z.squeeze(0).float()

def refine_image(pil_in: Image.Image, all_text_feats: torch.Tensor, pos_idx: int):
    Z_init = vae_encode_from_pil(pil_in)     # [4,64,64] float32
    z = Z_init.clone().detach().to(DEVICE).requires_grad_(True)
    opt = torch.optim.Adam([z], lr=LR)

    with torch.no_grad():
        baseline = T.Resize((512,512))(T.ToTensor()(pil_in)).unsqueeze(0).to(DEVICE, dtype=DTYPE)

    for _ in range(REFINE_STEPS):
        opt.zero_grad()
        x = vae.decode((z / VAE_SCALE).to(DTYPE).unsqueeze(0)).sample
        x = (x/2 + 0.5).clamp(0,1)
        arr = (x[0].detach().float().cpu().permute(1,2,0).numpy()*255).astype(np.uint8)
        pil = Image.fromarray(arr)
        f_img = clip_img_feats_from_pil(pil).unsqueeze(0).to(DEVICE)  # [1,D]
        # full InfoNCE vs ALL prototypes
        logits = (f_img @ all_text_feats.T) / 0.07
        labels = torch.tensor([pos_idx], dtype=torch.long, device=DEVICE)
        loss_c = F.cross_entropy(logits, labels)
        loss_v = F.mse_loss(z, Z_init.to(DEVICE))
        loss_p = lpips_fn(x*2-1, baseline*2-1).mean()
        loss = LAMB_C*loss_c + LAMB_V*loss_v + LAMB_P*loss_p
        loss.backward()
        opt.step()

    x_fin = vae.decode((z / VAE_SCALE).to(DTYPE).unsqueeze(0)).sample
    x_fin = (x_fin/2 + 0.5).clamp(0,1)
    arr = (x_fin[0].detach().float().cpu().permute(1,2,0).numpy()*255).astype(np.uint8)
    return Image.fromarray(arr).resize((256,256), Image.BICUBIC)

def run_vlpr(out_dir, records, text_proto_by_class):
    # build global negative bank of all text prototypes
    all_text_feats, pos_map = [], {}
    idx = 0
    for cname, tplist in text_proto_by_class.items():
        for k, (t, _) in enumerate(tplist):
            ip = clip_proc(text=[t], return_tensors="pt", padding=True, truncation=True).to(DEVICE)
            f = clip_model.get_text_features(**ip)
            f = F.normalize(f, dim=-1).squeeze(0).detach()
            all_text_feats.append(f); pos_map[(cname,k)] = idx; idx += 1
    all_text_feats = torch.stack(all_text_feats).to(DEVICE)

    # refine each sample contrastively
    for (cname, k, fn, _) in tqdm(records, desc="VLPR refining"):
        img = Image.open(out_dir/cname/fn).convert("RGB")
        pos_idx = pos_map[(cname,k)]
        refined = refine_image(img, all_text_feats, pos_idx)
        refined.save(out_dir/cname/f"{Path(fn).stem}_refined.png")

# ======================== 11. EVAL: ResNetAP-10 + hard-label ======
# ResNetAP-10: avg-pooling downsampling + GroupNorm/InstanceNorm (BN-free).
# Epoch counts follow Minimax supplemental Table 6.  :contentReference[oaicite:6]{index=6}

def get_eval_epochs(ipc):
    return 2000 if ipc<=10 else 1500

# Building blocks
class BasicBlockAP(nn.Module):
    expansion = 1
    def __init__(self, in_planes, planes, downsample=False, groups_gn=None):
        super().__init__()
        stride = 1
        self.conv1 = nn.Conv2d(in_planes, planes, kernel_size=3, stride=stride, padding=1, bias=False)
        self.gn1   = nn.GroupNorm(groups_gn if groups_gn else planes, planes)
        self.relu  = nn.ReLU(inplace=True)
        self.conv2 = nn.Conv2d(planes, planes, kernel_size=3, stride=1, padding=1, bias=False)
        self.gn2   = nn.GroupNorm(groups_gn if groups_gn else planes, planes)
        self.downsample = downsample
        if downsample:
            self.pool = nn.AvgPool2d(kernel_size=2, stride=2)

    def forward(self, x):
        identity = x
        out = self.conv1(x); out = self.gn1(out); out = self.relu(out)
        out = self.conv2(out); out = self.gn2(out)
        if self.downsample:
            identity = self.pool(identity)
            out = self.pool(out)
        out += identity
        out = self.relu(out)
        return out

class ResNetAP10(nn.Module):
    # Roughly: conv(7x7,1) -> avgpool(4x4,s4) -> [64]->[128]->[256]->[512] with AP downsampling
    # using GroupNorm with num_groups=channels (InstanceNorm-like).  :contentReference[oaicite:7]{index=7}
    def __init__(self, num_classes=10):
        super().__init__()
        self.stem = nn.Sequential(
            nn.Conv2d(3,64,kernel_size=7,stride=1,padding=3,bias=False),
            nn.GroupNorm(64,64),
            nn.ReLU(inplace=True),
            nn.AvgPool2d(kernel_size=4, stride=4)  # early downsampling
        )
        self.layer1 = BasicBlockAP(64, 64,  downsample=False, groups_gn=64)
        self.layer2 = BasicBlockAP(64, 128, downsample=True,  groups_gn=128)
        self.layer3 = BasicBlockAP(128,256, downsample=True,  groups_gn=256)
        self.layer4 = BasicBlockAP(256,512, downsample=True,  groups_gn=512)
        self.pool   = nn.AdaptiveAvgPool2d(1)
        self.fc     = nn.Linear(512, num_classes)

        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')

    def forward(self,x):
        x = self.stem(x)
        x = self.layer1(x); x = self.layer2(x); x = self.layer3(x); x = self.layer4(x)
        x = self.pool(x).flatten(1)
        return self.fc(x)

# CutMix utility (helps on tiny synthetic sets).  :contentReference[oaicite:8]{index=8}
def rand_bbox(W, H, lam):
    cut_rat = np.sqrt(1. - lam)
    cut_w, cut_h = int(W*cut_rat), int(H*cut_rat)
    cx, cy = np.random.randint(W), np.random.randint(H)
    x1, y1 = np.clip(cx - cut_w//2, 0, W), np.clip(cy - cut_h//2, 0, H)
    x2, y2 = np.clip(cx + cut_w//2, 0, W), np.clip(cy + cut_h//2, 0, H)
    return x1, y1, x2, y2

def make_eval_loaders(distilled_root, val_root, batch=64, workers=2):
    train_tfms = T.Compose([
        T.RandomResizedCrop(224, scale=(0.2, 1.0), interpolation=T.InterpolationMode.BICUBIC),
        T.RandomHorizontalFlip(),
        T.ToTensor(),
        T.Normalize(mean=[0.485,0.456,0.406], std=[0.229,0.224,0.225]),
    ])
    val_tfms = T.Compose([
        T.Resize(256, interpolation=T.InterpolationMode.BICUBIC),
        T.CenterCrop(224),
        T.ToTensor(),
        T.Normalize(mean=[0.485,0.456,0.406], std=[0.229,0.224,0.225]),
    ])
    train_ds = ImageFolder(distilled_root, transform=train_tfms)
    val_ds   = ImageFolder(val_root,      transform=val_tfms)
    train_loader = DataLoader(train_ds, batch_size=batch, shuffle=True,  num_workers=workers, pin_memory=True)
    val_loader   = DataLoader(val_ds,   batch_size=batch, shuffle=False, num_workers=workers, pin_memory=True)
    return train_loader, val_loader

def train_eval(distilled_root, epochs):
    train_loader, val_loader = make_eval_loaders(distilled_root, IMAGENETTE_PATH/"val", batch=64, workers=2)
    model = ResNetAP10(num_classes=10).to(DEVICE)
    opt = torch.optim.SGD(model.parameters(), lr=0.1, momentum=0.9, weight_decay=5e-4)
    # Step schedule roughly at 30% and 60% of total epochs (hard-label style).  :contentReference[oaicite:9]{index=9}
    milestones = [int(0.3*epochs), int(0.6*epochs)]
    sched = torch.optim.lr_scheduler.MultiStepLR(opt, milestones=milestones, gamma=0.1)
    ce = nn.CrossEntropyLoss()

    def evaluate():
        model.eval(); correct=0; total=0
        with torch.no_grad():
            for x,y in val_loader:
                x,y = x.to(DEVICE), y.to(DEVICE)
                pred = model(x).argmax(1)
                correct += (pred==y).sum().item(); total += y.numel()
        return correct/total

    print(f"Training ResNetAP-10 for {epochs} epochs")
    report_points = set([1,10,50,100,200,500,1000,epochs])
    cutmix_alpha = 1.0

    for ep in range(1, epochs+1):
        model.train()
        for x,y in train_loader:
            x,y = x.to(DEVICE), y.to(DEVICE)
            # CutMix
            lam = np.random.beta(cutmix_alpha, cutmix_alpha)
            idx = torch.randperm(x.size(0), device=DEVICE)
            y2 = y[idx]
            bbx1,bby1,bbx2,bby2 = rand_bbox(x.size(3), x.size(2), lam)
            x2 = x[idx]
            x[:,:,bby1:bby2,bbx1:bbx2] = x2[:,:,bby1:bby2,bbx1:bbx2]
            lam_adj = 1 - ((bbx2-bbx1)*(bby2-bby1)/(x.size(-1)*x.size(-2)))
            logits = model(x)
            loss = lam_adj*ce(logits,y) + (1-lam_adj)*ce(logits,y2)
            opt.zero_grad(); loss.backward(); opt.step()
        sched.step()
        if ep in report_points:
            acc = evaluate()
            print(f"[{distilled_root.name}] Epoch {ep:04d}/{epochs}  Val@1: {acc*100:.2f}%")
    return evaluate()

# ======================== 12. MAIN: IPC {10,20,50} =================
ipc_list = [10, 20, 50]
final_results = {}
for IPC in ipc_list:
    print(f"\n===== IPC = {IPC} =====")
    # Phase 1: JVL-C
    clusters_by_class = jvlc_build_prototypes(IPC)
    # Phase 2: CCSO
    text_proto_by_class = ccso_select(clusters_by_class)
    # Phase 3: Synthesis
    out_dir, records = synthesize_for_ipc(IPC, clusters_by_class, text_proto_by_class, OUTPUT_ROOT)
    # Phase 4: VLPR refinement
    run_vlpr(out_dir, records, text_proto_by_class)
    # Phase 5: Collect refined images
    final_dir = Path(OUTPUT_ROOT)/f"ipc{IPC}_final"
    if final_dir.exists(): shutil.rmtree(final_dir)
    for cname in WNID2NAME.values():
        (final_dir/cname).mkdir(parents=True, exist_ok=True)
        for p in (out_dir/cname).glob("*_refined.png"):
            shutil.copy(p, final_dir/cname/p.name)
    # Phase 6: Evaluation (hard-label protocol, matching epochs)
    E = get_eval_epochs(IPC)
    acc = train_eval(final_dir, E)
    final_results[IPC] = acc
    torch.cuda.empty_cache()

print("\n========== RESULTS ==========")
for k,v in final_results.items():
    print(f"IPC {k}: {v*100:.2f}%")